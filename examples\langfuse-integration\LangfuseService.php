<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class LangfuseService
{
    private string $baseUrl;
    private string $publicKey;
    private string $secretKey;
    
    public function __construct()
    {
        $this->baseUrl = config('services.langfuse.base_url', 'https://cloud.langfuse.com');
        $this->publicKey = config('services.langfuse.public_key');
        $this->secretKey = config('services.langfuse.secret_key');
    }
    
    /**
     * Create a new trace for a chat session
     */
    public function createTrace(string $sessionId, ?int $userId = null): array
    {
        $payload = [
            'id' => $sessionId,
            'name' => 'chat-session',
            'userId' => $userId ? (string) $userId : null,
            'sessionId' => $sessionId,
            'metadata' => [
                'application' => 'rydo-backend',
                'environment' => app()->environment(),
            ],
            'tags' => ['chat', 'openai'],
        ];
        
        return $this->makeRequest('POST', '/api/public/traces', $payload);
    }
    
    /**
     * Create a generation (LLM call) within a trace
     */
    public function createGeneration(array $params): array
    {
        $payload = [
            'id' => $params['id'] ?? \Str::uuid(),
            'traceId' => $params['trace_id'],
            'name' => $params['name'] ?? 'openai-chat',
            'startTime' => $params['start_time'] ?? now()->toISOString(),
            'endTime' => $params['end_time'] ?? now()->toISOString(),
            'model' => $params['model'] ?? 'gpt-4',
            'modelParameters' => [
                'temperature' => $params['temperature'] ?? 0.7,
                'maxTokens' => $params['max_tokens'] ?? 1000,
            ],
            'input' => $params['messages'] ?? [],
            'output' => $params['response'] ?? '',
            'usage' => [
                'promptTokens' => $params['prompt_tokens'] ?? 0,
                'completionTokens' => $params['completion_tokens'] ?? 0,
                'totalTokens' => $params['total_tokens'] ?? 0,
            ],
            'metadata' => $params['metadata'] ?? [],
        ];
        
        return $this->makeRequest('POST', '/api/public/generations', $payload);
    }
    
    /**
     * Update a generation with completion data
     */
    public function updateGeneration(string $generationId, array $params): array
    {
        $payload = [
            'endTime' => $params['end_time'] ?? now()->toISOString(),
            'output' => $params['response'] ?? '',
            'usage' => [
                'promptTokens' => $params['prompt_tokens'] ?? 0,
                'completionTokens' => $params['completion_tokens'] ?? 0,
                'totalTokens' => $params['total_tokens'] ?? 0,
            ],
            'metadata' => $params['metadata'] ?? [],
        ];
        
        return $this->makeRequest('PATCH', "/api/public/generations/{$generationId}", $payload);
    }
    
    /**
     * Create a span for custom operations
     */
    public function createSpan(array $params): array
    {
        $payload = [
            'id' => $params['id'] ?? \Str::uuid(),
            'traceId' => $params['trace_id'],
            'name' => $params['name'],
            'startTime' => $params['start_time'] ?? now()->toISOString(),
            'endTime' => $params['end_time'] ?? now()->toISOString(),
            'input' => $params['input'] ?? null,
            'output' => $params['output'] ?? null,
            'metadata' => $params['metadata'] ?? [],
        ];
        
        return $this->makeRequest('POST', '/api/public/spans', $payload);
    }
    
    /**
     * Make HTTP request to Langfuse API
     */
    private function makeRequest(string $method, string $endpoint, array $payload = []): array
    {
        try {
            $response = Http::withBasicAuth($this->publicKey, $this->secretKey)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->timeout(10)
                ->{strtolower($method)}($this->baseUrl . $endpoint, $payload);
            
            if ($response->successful()) {
                return $response->json() ?? [];
            }
            
            Log::warning('Langfuse API error', [
                'status' => $response->status(),
                'response' => $response->body(),
                'endpoint' => $endpoint,
            ]);
            
            return [];
            
        } catch (\Exception $e) {
            Log::error('Langfuse API exception', [
                'error' => $e->getMessage(),
                'endpoint' => $endpoint,
            ]);
            
            return [];
        }
    }
}
