<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OpenAIService
{
    private string $pythonServiceUrl;
    
    public function __construct()
    {
        $this->pythonServiceUrl = config('services.agentops.python_service_url', 'http://localhost:5000');
    }
    
    /**
     * Send chat completion request through AgentOps-enabled Python service
     */
    public function chatCompletion(array $messages, array $options = []): array
    {
        $payload = [
            'model' => $options['model'] ?? 'gpt-4',
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 1000,
            'session_id' => $options['session_id'] ?? null,
            'user_id' => $options['user_id'] ?? null,
        ];
        
        try {
            $response = Http::timeout(30)
                ->post($this->pythonServiceUrl . '/chat/completions', $payload);
            
            if ($response->successful()) {
                return $response->json();
            }
            
            throw new \Exception('Python service error: ' . $response->body());
            
        } catch (\Exception $e) {
            Log::error('AgentOps Python service error', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            
            // Fallback to direct OpenAI call if Python service fails
            return $this->fallbackDirectOpenAI($messages, $options);
        }
    }
    
    /**
     * Fallback to direct OpenAI API call
     */
    private function fallbackDirectOpenAI(array $messages, array $options = []): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . config('services.openai.api_key'),
            'Content-Type' => 'application/json',
        ])->post('https://api.openai.com/v1/chat/completions', [
            'model' => $options['model'] ?? 'gpt-4',
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 1000,
        ]);
        
        return $response->json();
    }
    
    /**
     * Check if Python service is healthy
     */
    public function isHealthy(): bool
    {
        try {
            $response = Http::timeout(5)->get($this->pythonServiceUrl . '/health');
            return $response->successful();
        } catch (\Exception $e) {
            return false;
        }
    }
}
