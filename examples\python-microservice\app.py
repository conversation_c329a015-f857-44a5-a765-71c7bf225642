from flask import Flask, request, jsonify, Response
from flask_cors import CORS
from openai import OpenAI
import os
import uuid
import json
import time
import logging
from datetime import datetime, timezone
from dotenv import load_dotenv
from hybrid_agentops_service import HybridAgentOpsService, track_operation

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for Laravel frontend

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# Initialize Hybrid AgentOps Service
agentops_service = None
try:
    agentops_service = HybridAgentOpsService(
        api_key=os.getenv('AGENTOPS_API_KEY'),
        use_sdk=os.getenv('AGENTOPS_USE_SDK', 'true').lower() == 'true',
        use_rest=os.getenv('AGENTOPS_USE_REST', 'true').lower() == 'true'
    )
    logger.info("Hybrid AgentOps service initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize AgentOps service: {e}")
    agentops_service = None


class RydoChatAgent:
    """Enhanced chat agent with hybrid AgentOps tracking"""

    def __init__(self):
        self.client = client
        self.agentops_service = agentops_service

    @track_operation("chat-completion", agentops_service)
    def generate_chat_completion(self, messages, model='gpt-4', **kwargs):
        """Generate chat completion with hybrid AgentOps tracking"""
        response = self.client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=kwargs.get('temperature', 0.7),
            max_tokens=kwargs.get('max_tokens', 1000)
        )

        # Track with hybrid service
        if self.agentops_service:
            try:
                response_dict = response.model_dump()
                self.agentops_service.track_llm_call(
                    model=model,
                    messages=messages,
                    response=response_dict
                )
            except Exception as e:
                logger.error(f"Failed to track LLM call: {e}")

        return response

    @track_operation("chat-stream", agentops_service)
    def generate_chat_stream(self, messages, model='gpt-4', **kwargs):
        """Generate streaming chat completion with hybrid AgentOps tracking"""
        return self.client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=kwargs.get('temperature', 0.7),
            max_tokens=kwargs.get('max_tokens', 1000),
            stream=True
        )

# Initialize the agent
chat_agent = RydoChatAgent()

@app.route('/chat/completions', methods=['POST'])
def chat_completions():
    """Handle regular chat completions with hybrid AgentOps tracking"""
    session_id = None
    try:
        data = request.json

        # Extract Laravel session info
        session_id = data.get('session_id', str(uuid.uuid4()))
        user_id = data.get('user_id')
        agent_id = data.get('agent_id')
        chat_message_id = data.get('chat_message_id')

        # Start AgentOps session with hybrid service
        if agentops_service:
            try:
                agentops_service.start_session(
                    session_id=session_id,
                    tags=["chat-completion"],
                    user_id=user_id,
                    agent_id=agent_id
                )
                logger.info(f"Started hybrid AgentOps session: {session_id}")
            except Exception as e:
                logger.error(f"Failed to start AgentOps session: {e}")

        # Make OpenAI call through enhanced agent
        response = chat_agent.generate_chat_completion(
            messages=data.get('messages', []),
            model=data.get('model', 'gpt-4'),
            temperature=data.get('temperature', 0.7),
            max_tokens=data.get('max_tokens', 1000)
        )

        # End session with success
        if agentops_service:
            try:
                agentops_service.end_session('Success')
                logger.info(f"Ended hybrid AgentOps session: {session_id}")
            except Exception as e:
                logger.error(f"Failed to end AgentOps session: {e}")

        return jsonify({
            'id': response.id,
            'object': response.object,
            'created': response.created,
            'model': response.model,
            'choices': [
                {
                    'index': choice.index,
                    'message': {
                        'role': choice.message.role,
                        'content': choice.message.content
                    },
                    'finish_reason': choice.finish_reason
                } for choice in response.choices
            ],
            'usage': {
                'prompt_tokens': response.usage.prompt_tokens,
                'completion_tokens': response.usage.completion_tokens,
                'total_tokens': response.usage.total_tokens
            },
            'agentops_session_id': session_id,
            'tracking_method': 'hybrid'
        })

    except Exception as e:
        # End session with error
        if agentops_service and session_id:
            try:
                agentops_service.end_session('Error', str(e))
            except Exception as end_error:
                logger.error(f"Failed to end AgentOps session on error: {end_error}")

        logger.error(f"Chat completion error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/chat/stream', methods=['POST'])
def chat_stream():
    """Handle streaming chat completions with hybrid AgentOps tracking"""
    session_id = None
    try:
        data = request.json

        # Extract Laravel session info
        session_id = data.get('session_id', str(uuid.uuid4()))
        user_id = data.get('user_id')
        agent_id = data.get('agent_id')

        # Start AgentOps session with hybrid service
        if agentops_service:
            try:
                agentops_service.start_session(
                    session_id=session_id,
                    tags=["chat-stream"],
                    user_id=user_id,
                    agent_id=agent_id
                )
                logger.info(f"Started hybrid AgentOps streaming session: {session_id}")
            except Exception as e:
                logger.error(f"Failed to start AgentOps streaming session: {e}")

        def generate_stream():
            try:
                # Make streaming OpenAI call through AgentOps-tracked agent
                stream = chat_agent.generate_chat_stream(
                    messages=data.get('messages', []),
                    model=data.get('model', 'gpt-4'),
                    temperature=data.get('temperature', 0.7),
                    max_tokens=data.get('max_tokens', 1000)
                )

                full_response = ''
                for chunk in stream:
                    if chunk.choices[0].delta.content is not None:
                        content = chunk.choices[0].delta.content
                        full_response += content

                        # Send chunk in Laravel-compatible format
                        chunk_data = {
                            'id': chunk.id,
                            'content': content,
                            'type': 'text',
                            'sender': 'agent',
                            'timestamp': time.time(),
                            'metadata': {
                                'chat_session_id': session_id,
                                'agent_id': agent_id,
                                'agentops_tracked': True
                            }
                        }

                        yield f"data: {json.dumps(chunk_data)}\n\n"

                # Send completion message
                completion_data = {
                    'id': chunk.id,
                    'content': '',
                    'type': 'completion',
                    'sender': 'agent',
                    'timestamp': time.time(),
                    'metadata': {
                        'completed': True,
                        'chat_session_id': session_id,
                        'agent_id': agent_id,
                        'full_response': full_response,
                        'agentops_tracked': True
                    }
                }
                yield f"data: {json.dumps(completion_data)}\n\n"

                # End AgentOps session
                if agentops_service:
                    try:
                        agentops_service.end_session('Success')
                        logger.info(f"Ended hybrid AgentOps streaming session: {session_id}")
                    except Exception as e:
                        logger.error(f"Failed to end AgentOps streaming session: {e}")

            except Exception as e:
                # End session with error
                if agentops_service:
                    try:
                        agentops_service.end_session('Error', str(e))
                    except Exception as end_error:
                        logger.error(f"Failed to end AgentOps streaming session on error: {end_error}")

                error_data = {
                    'id': str(uuid.uuid4()),
                    'content': '',
                    'type': 'error',
                    'sender': 'system',
                    'timestamp': time.time(),
                    'metadata': {
                        'error': str(e),
                        'chat_session_id': session_id
                    }
                }
                yield f"data: {json.dumps(error_data)}\n\n"

        return Response(
            generate_stream(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
            }
        )

    except Exception as e:
        # End session with error
        if agentops_service and session_id:
            try:
                agentops_service.end_session('Error', str(e))
            except Exception as end_error:
                logger.error(f"Failed to end AgentOps streaming session on error: {end_error}")

        logger.error(f"Chat streaming error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Enhanced health check endpoint with hybrid service status"""
    health_status = {
        'status': 'healthy',
        'service': 'rydo-agentops-hybrid-proxy',
        'timestamp': datetime.now(timezone.utc).isoformat()
    }

    if agentops_service:
        health_status.update(agentops_service.get_health_status())
    else:
        health_status.update({
            'agentops_available': False,
            'error': 'AgentOps service not initialized'
        })

    return jsonify(health_status)

@app.route('/metrics', methods=['GET'])
def metrics():
    """Enhanced metrics endpoint with hybrid service information"""
    metrics_data = {
        'service': 'rydo-agentops-hybrid-proxy',
        'openai_configured': bool(os.getenv('OPENAI_API_KEY')),
        'agentops_configured': bool(os.getenv('AGENTOPS_API_KEY')),
        'timestamp': datetime.now(timezone.utc).isoformat()
    }

    if agentops_service:
        metrics_data.update(agentops_service.get_metrics())
    else:
        metrics_data.update({
            'agentops_available': False,
            'error': 'AgentOps service not initialized'
        })

    return jsonify(metrics_data)

# Additional REST API endpoints for direct AgentOps integration
@app.route('/agentops/session', methods=['POST'])
def create_agentops_session():
    """Create a new AgentOps session via REST API"""
    try:
        data = request.json
        session_id = data.get('session_id', str(uuid.uuid4()))
        tags = data.get('tags', [])
        user_id = data.get('user_id')
        agent_id = data.get('agent_id')

        if agentops_service:
            session_id = agentops_service.start_session(
                session_id=session_id,
                tags=tags,
                user_id=user_id,
                agent_id=agent_id
            )
            return jsonify({
                'success': True,
                'session_id': session_id,
                'method': 'hybrid'
            })
        else:
            return jsonify({'error': 'AgentOps service not available'}), 503

    except Exception as e:
        logger.error(f"Failed to create AgentOps session: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/agentops/session/<session_id>', methods=['PUT'])
def update_agentops_session(session_id):
    """Update/end an AgentOps session via REST API"""
    try:
        data = request.json
        end_state = data.get('end_state', 'Success')
        reason = data.get('reason')

        if agentops_service:
            success = agentops_service.end_session(end_state, reason)
            return jsonify({
                'success': success,
                'session_id': session_id,
                'end_state': end_state
            })
        else:
            return jsonify({'error': 'AgentOps service not available'}), 503

    except Exception as e:
        logger.error(f"Failed to update AgentOps session: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/agentops/events', methods=['POST'])
def create_agentops_events():
    """Create events in AgentOps via REST API"""
    try:
        data = request.json
        events = data.get('events', [])

        if agentops_service and agentops_service.use_rest:
            response = agentops_service.rest_client.create_events(events)
            return jsonify({
                'success': True,
                'events_created': len(events),
                'response': response
            })
        else:
            return jsonify({'error': 'AgentOps REST API not available'}), 503

    except Exception as e:
        logger.error(f"Failed to create AgentOps events: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/agentops/agent', methods=['POST'])
def create_agentops_agent():
    """Create/register an agent in AgentOps via REST API"""
    try:
        data = request.json
        agent_id = data.get('agent_id', str(uuid.uuid4()))
        name = data.get('name', f'Agent-{agent_id}')

        if agentops_service:
            success = agentops_service.create_agent(agent_id, name)
            return jsonify({
                'success': success,
                'agent_id': agent_id,
                'name': name
            })
        else:
            return jsonify({'error': 'AgentOps service not available'}), 503

    except Exception as e:
        logger.error(f"Failed to create AgentOps agent: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
