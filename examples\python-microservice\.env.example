# AgentOps Configuration
AGENTOPS_API_KEY=your-agentops-api-key-here
AGENTOPS_USE_SDK=true
AGENTOPS_USE_REST=true

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=false

# Service Configuration
SERVICE_PORT=5000
SERVICE_HOST=0.0.0.0

# Logging Configuration
LOG_LEVEL=INFO

# Optional: AgentOps REST API Base URL (defaults to https://api.agentops.ai)
# AGENTOPS_BASE_URL=https://api.agentops.ai
