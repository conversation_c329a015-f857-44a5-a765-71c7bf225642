<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\OpenAIService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ChatController extends Controller
{
    public function __construct(
        private OpenAIService $openAIService
    ) {}
    
    public function sendMessage(Request $request): JsonResponse
    {
        $request->validate([
            'message' => 'required|string',
            'session_id' => 'required|string',
            'model' => 'sometimes|string|in:gpt-4,gpt-3.5-turbo',
        ]);
        
        try {
            // Build conversation history
            $messages = $this->buildConversationHistory($request->session_id);
            $messages[] = [
                'role' => 'user',
                'content' => $request->message
            ];
            
            // Send to AgentOps-enabled Python service
            $response = $this->openAIService->chatCompletion($messages, [
                'model' => $request->model ?? 'gpt-4',
                'session_id' => $request->session_id,
                'user_id' => auth()->id(),
                'temperature' => 0.7,
                'max_tokens' => 1000,
            ]);
            
            // Save the conversation to database
            $this->saveConversation(
                $request->session_id,
                $request->message,
                $response['choices'][0]['message']['content'],
                $response['usage'] ?? []
            );
            
            return response()->json([
                'message' => $response['choices'][0]['message']['content'],
                'usage' => $response['usage'] ?? [],
                'session_id' => $request->session_id,
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to process message',
                'details' => $e->getMessage()
            ], 500);
        }
    }
    
    private function buildConversationHistory(string $sessionId): array
    {
        // Retrieve conversation history from your database
        // This is a simplified example
        $messages = \DB::table('chat_messages')
            ->where('session_id', $sessionId)
            ->orderBy('created_at')
            ->get()
            ->map(function ($message) {
                return [
                    'role' => $message->role,
                    'content' => $message->content
                ];
            })
            ->toArray();
            
        return $messages;
    }
    
    private function saveConversation(string $sessionId, string $userMessage, string $assistantMessage, array $usage): void
    {
        // Save user message
        \DB::table('chat_messages')->insert([
            'session_id' => $sessionId,
            'role' => 'user',
            'content' => $userMessage,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        // Save assistant message
        \DB::table('chat_messages')->insert([
            'session_id' => $sessionId,
            'role' => 'assistant',
            'content' => $assistantMessage,
            'prompt_tokens' => $usage['prompt_tokens'] ?? 0,
            'completion_tokens' => $usage['completion_tokens'] ?? 0,
            'total_tokens' => $usage['total_tokens'] ?? 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
