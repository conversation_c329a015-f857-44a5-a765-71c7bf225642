version: '3.8'

services:
  agentops-service:
    build: .
    ports:
      - "5000:5000"
    environment:
      - AGENTOPS_API_KEY=${AGENTOPS_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - FLASK_ENV=production
    env_file:
      - .env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - ./logs:/app/logs
    networks:
      - rydo-network

networks:
  rydo-network:
    driver: bridge
