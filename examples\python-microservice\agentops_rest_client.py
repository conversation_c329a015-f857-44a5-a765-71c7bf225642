"""
AgentOps REST API Client
Direct integration with AgentOps API using REST endpoints
"""

import requests
import uuid
import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)


class AgentOpsRestClient:
    """Direct REST API client for AgentOps integration"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.agentops.ai"):
        self.api_key = api_key
        self.base_url = base_url
        self.jwt_token = None
        self.current_session_id = None
        self.session_start_time = None
        
    def _make_request(self, method: str, endpoint: str, data: Dict = None, use_jwt: bool = True) -> Dict:
        """Make HTTP request to AgentOps API"""
        url = f"{self.base_url}{endpoint}"
        headers = {"Content-Type": "application/json"}
        
        if use_jwt and self.jwt_token:
            headers["Authorization"] = f"Bearer {self.jwt_token}"
        elif not use_jwt:
            headers["X-Agentops-Api-Key"] = self.api_key
            
        try:
            response = requests.request(method, url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"AgentOps API request failed: {e}")
            raise
            
    def create_session(self, session_id: str = None, tags: List[str] = None, 
                      host_env: Dict = None) -> Dict:
        """Create a new AgentOps session"""
        if not session_id:
            session_id = str(uuid.uuid4())
            
        self.current_session_id = session_id
        self.session_start_time = datetime.now(timezone.utc)
        
        session_data = {
            "session": {
                "id": session_id,
                "init_timestamp": self.session_start_time.isoformat(),
                "tags": tags or ["rydo-backend", "rest-api"],
                "host_env": host_env or self._get_default_host_env()
            }
        }
        
        try:
            response = self._make_request("POST", "/v2/create_session", session_data, use_jwt=False)
            self.jwt_token = response.get("jwt")
            logger.info(f"AgentOps session created: {session_id}")
            return response
        except Exception as e:
            logger.error(f"Failed to create AgentOps session: {e}")
            raise
            
    def update_session(self, end_state: str = "Success", end_state_reason: str = None, 
                      tags: List[str] = None) -> Dict:
        """Update/end an AgentOps session"""
        if not self.current_session_id:
            raise ValueError("No active session to update")
            
        session_data = {
            "session": {
                "id": self.current_session_id,
                "end_timestamp": datetime.now(timezone.utc).isoformat(),
                "end_state": end_state,
                "end_state_reason": end_state_reason,
                "tags": tags
            }
        }
        
        try:
            response = self._make_request("POST", "/v2/update_session", session_data)
            logger.info(f"AgentOps session updated: {self.current_session_id}")
            return response
        except Exception as e:
            logger.error(f"Failed to update AgentOps session: {e}")
            raise
            
    def create_events(self, events: List[Dict]) -> Dict:
        """Create events in AgentOps"""
        if not self.jwt_token:
            raise ValueError("No active session - create session first")
            
        event_data = {"events": events}
        
        try:
            response = self._make_request("POST", "/v2/create_events", event_data)
            logger.info(f"Created {len(events)} AgentOps events")
            return response
        except Exception as e:
            logger.error(f"Failed to create AgentOps events: {e}")
            raise
            
    def create_llm_event(self, model: str, prompt: Any, completion: Any = None,
                        prompt_tokens: int = None, completion_tokens: int = None,
                        init_timestamp: str = None, end_timestamp: str = None) -> Dict:
        """Create an LLM event"""
        event = {
            "type": "llm",
            "init_timestamp": init_timestamp or datetime.now(timezone.utc).isoformat(),
            "model": model,
            "prompt": prompt,
            "prompt_tokens": prompt_tokens,
        }
        
        if completion is not None:
            event["completion"] = completion
            event["end_timestamp"] = end_timestamp or datetime.now(timezone.utc).isoformat()
            
        if completion_tokens is not None:
            event["completion_tokens"] = completion_tokens
            
        return self.create_events([event])
        
    def create_tool_event(self, name: str, input_data: Any, output_data: Any = None,
                         init_timestamp: str = None, end_timestamp: str = None) -> Dict:
        """Create a tool usage event"""
        event = {
            "type": "tool",
            "name": name,
            "init_timestamp": init_timestamp or datetime.now(timezone.utc).isoformat(),
            "input": input_data,
        }
        
        if output_data is not None:
            event["output"] = output_data
            event["end_timestamp"] = end_timestamp or datetime.now(timezone.utc).isoformat()
            
        return self.create_events([event])
        
    def create_agent(self, agent_id: str, name: str) -> Dict:
        """Create/register an agent"""
        agent_data = {
            "id": agent_id,
            "name": name
        }
        
        try:
            response = self._make_request("POST", "/v2/create_agent", agent_data)
            logger.info(f"AgentOps agent created: {agent_id}")
            return response
        except Exception as e:
            logger.error(f"Failed to create AgentOps agent: {e}")
            raise
            
    def refresh_token(self) -> Dict:
        """Refresh JWT token"""
        if not self.current_session_id:
            raise ValueError("No active session to refresh token for")
            
        refresh_data = {"session_id": self.current_session_id}
        
        try:
            response = self._make_request("POST", "/v2/reauthorize_jwt", refresh_data, use_jwt=False)
            self.jwt_token = response.get("jwt")
            logger.info("AgentOps JWT token refreshed")
            return response
        except Exception as e:
            logger.error(f"Failed to refresh AgentOps JWT token: {e}")
            raise
            
    def _get_default_host_env(self) -> Dict:
        """Get default host environment information"""
        import platform
        import psutil
        import sys
        
        try:
            return {
                "OS": {
                    "OS": platform.system(),
                    "OS Release": platform.release(),
                    "OS Version": platform.version()
                },
                "CPU": {
                    "CPU Usage": f"{psutil.cpu_percent()}%",
                    "Total cores": psutil.cpu_count()
                },
                "RAM": {
                    "Used": f"{psutil.virtual_memory().used / (1024**3):.2f} GB",
                    "Total": f"{psutil.virtual_memory().total / (1024**3):.2f} GB"
                },
                "SDK": {
                    "Python Version": sys.version.split()[0],
                    "System Packages": {
                        "requests": requests.__version__,
                        "flask": "3.0.0"  # From requirements.txt
                    }
                }
            }
        except Exception as e:
            logger.warning(f"Failed to get host environment: {e}")
            return {}
            
    def is_healthy(self) -> bool:
        """Check if AgentOps API is accessible"""
        try:
            # Simple health check - try to make a basic request
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
