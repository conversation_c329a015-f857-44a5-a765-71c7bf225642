# Enhanced AgentOps Integration Guide for Rydo Backend

This guide provides complete instructions for integrating AgentOps with your Laravel + OpenAI application using both SDK and REST API approaches.

## Overview

Enhanced AgentOps integration uses a hybrid Python microservice approach:
```
Laravel App → Hybrid Python AgentOps Service → OpenAI API → AgentOps Dashboard
                    ↓
            [SDK Integration + REST API Integration]
```

## New Features in Enhanced Integration

- **Hybrid Approach**: Combines AgentOps SDK and REST API for maximum reliability
- **Automatic Fallback**: If SDK fails, REST API takes over (and vice versa)
- **Direct REST API Access**: Additional endpoints for direct AgentOps API integration
- **Enhanced Monitoring**: Comprehensive health checks and metrics
- **Better Error Handling**: Robust error handling with detailed logging

## Prerequisites

1. **AgentOps Account**: Sign up at [agentops.ai](https://agentops.ai) and get your API key
2. **Python 3.11+**: Required for the microservice
3. **Docker** (optional): For containerized deployment

## Setup Instructions

### 1. Configure Laravel Environment

Add these variables to your `.env` file:

```env
# AgentOps Configuration
AGENTOPS_ENABLED=true
AGENTOPS_API_KEY=your-agentops-api-key
AGENTOPS_SERVICE_URL=http://localhost:5000
AGENTOPS_FALLBACK_TO_OPENAI=true
```

### 2. Set Up Python Microservice

#### Option A: Local Development

1. **Navigate to the microservice directory:**
   ```bash
   cd examples/python-microservice
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Create environment file:**
   ```bash
   cp .env.example .env
   ```

5. **Configure the .env file:**
   ```env
   # AgentOps Configuration
   AGENTOPS_API_KEY=your-agentops-api-key
   AGENTOPS_USE_SDK=true
   AGENTOPS_USE_REST=true

   # OpenAI Configuration
   OPENAI_API_KEY=your-openai-api-key

   # Flask Configuration
   FLASK_ENV=production
   SERVICE_PORT=5000
   SERVICE_HOST=0.0.0.0

   # Logging Configuration
   LOG_LEVEL=INFO
   ```

6. **Run the service:**
   ```bash
   python app.py
   ```

#### Option B: Docker Deployment

1. **Navigate to the microservice directory:**
   ```bash
   cd examples/python-microservice
   ```

2. **Create environment file:**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Build and run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

### 3. Test the Integration

1. **Check AgentOps service health:**
   ```bash
   curl http://localhost:5000/health
   ```

2. **Check service metrics:**
   ```bash
   curl http://localhost:5000/metrics
   ```

3. **Test from Laravel:**
   Your existing chat functionality will automatically use AgentOps when enabled.

## Configuration Options

### Laravel Configuration

In `config/services.php`, you can configure:

```php
'agentops' => [
    'enabled' => env('AGENTOPS_ENABLED', false),
    'service_url' => env('AGENTOPS_SERVICE_URL', 'http://localhost:5000'),
    'api_key' => env('AGENTOPS_API_KEY'),
    'fallback_to_openai' => env('AGENTOPS_FALLBACK_TO_OPENAI', true),
],
```

### Enhanced AgentOps Features

The enhanced integration provides:

- **Hybrid Tracking**: Uses both SDK and REST API for maximum reliability
- **Automatic Session Tracking**: Each chat session is tracked in AgentOps
- **Cost Monitoring**: Token usage and costs are automatically calculated
- **Agent Behavior Tracking**: All AI interactions are logged with context
- **Error Handling**: Automatic fallback between SDK and REST API methods
- **Streaming Support**: Full support for streaming chat responses
- **Direct REST API Access**: Additional endpoints for custom integrations
- **Enhanced Health Monitoring**: Comprehensive service health checks

### New REST API Endpoints

The enhanced service provides additional REST API endpoints for direct AgentOps integration:

#### Create Session
```bash
POST /agentops/session
Content-Type: application/json

{
  "session_id": "optional-custom-id",
  "tags": ["custom", "tags"],
  "user_id": "user-123",
  "agent_id": "agent-456"
}
```

#### Update/End Session
```bash
PUT /agentops/session/{session_id}
Content-Type: application/json

{
  "end_state": "Success",
  "reason": "Task completed successfully"
}
```

#### Create Events
```bash
POST /agentops/events
Content-Type: application/json

{
  "events": [
    {
      "type": "llm",
      "model": "gpt-4",
      "prompt": "Hello world",
      "completion": "Hi there!",
      "prompt_tokens": 2,
      "completion_tokens": 2
    }
  ]
}
```

#### Create Agent
```bash
POST /agentops/agent
Content-Type: application/json

{
  "agent_id": "custom-agent-id",
  "name": "My Custom Agent"
}
```

## Monitoring and Observability

### AgentOps Dashboard

1. **Login to AgentOps**: Visit [app.agentops.ai](https://app.agentops.ai)
2. **View Sessions**: See all chat sessions from your Rydo app
3. **Analyze Costs**: Monitor token usage and costs per session
4. **Debug Issues**: Use session replay to debug AI behavior

### Laravel Logs

The integration logs important events:

```php
// AgentOps session creation
Log::info('AgentOps session created', [
    'session_id' => $agentOpsSessionId,
    'chat_session_id' => $chatSessionId,
    'user_id' => $userId,
]);

// Service health warnings
Log::warning('AgentOps is enabled but service is unhealthy, falling back to direct OpenAI');
```

## Deployment Considerations

### Production Deployment

1. **Service Reliability**: Deploy the Python service with proper monitoring
2. **Load Balancing**: Consider multiple instances for high traffic
3. **Network Security**: Ensure secure communication between Laravel and Python service
4. **Backup Strategy**: Always enable fallback to direct OpenAI

### Scaling

- **Horizontal Scaling**: Run multiple instances of the Python service
- **Load Balancer**: Use nginx or similar to distribute requests
- **Health Checks**: Implement proper health monitoring

## Troubleshooting

### Common Issues

1. **Service Not Responding**:
   - Check if Python service is running: `curl http://localhost:5000/health`
   - Verify environment variables are set correctly
   - Check logs for errors

2. **AgentOps Not Tracking**:
   - Verify AGENTOPS_API_KEY is correct
   - Check AgentOps dashboard for sessions
   - Ensure AGENTOPS_ENABLED=true in Laravel

3. **Fallback to OpenAI**:
   - This is expected behavior when AgentOps service is unavailable
   - Check Laravel logs for specific error messages

### Debug Commands

```bash
# Check service status
curl http://localhost:5000/health

# View service metrics
curl http://localhost:5000/metrics

# Test chat completion
curl -X POST http://localhost:5000/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [{"role": "user", "content": "Hello"}],
    "session_id": "test-session",
    "user_id": "test-user"
  }'
```

## Benefits of This Integration

1. **Zero Code Changes**: Your existing Laravel controllers work unchanged
2. **Automatic Fallback**: Continues working even if AgentOps service fails
3. **Rich Monitoring**: Complete visibility into AI agent behavior
4. **Cost Tracking**: Detailed cost analysis per session and user
5. **Session Replay**: Debug AI interactions with time-travel debugging
6. **Production Ready**: Robust error handling and logging

## Next Steps

1. **Enable AgentOps**: Set `AGENTOPS_ENABLED=true` in your `.env`
2. **Deploy Python Service**: Choose local or Docker deployment
3. **Monitor Dashboard**: Check AgentOps dashboard for session data
4. **Optimize Performance**: Monitor and tune based on usage patterns

For support, check the AgentOps documentation at [docs.agentops.ai](https://docs.agentops.ai) or contact their support team.
