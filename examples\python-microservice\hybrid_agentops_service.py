"""
Hybrid AgentOps Service
Combines SDK and REST API approaches for maximum reliability and flexibility
"""

import os
import uuid
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from agentops_rest_client import AgentOpsRestClient

# Try to import AgentOps SDK
try:
    import agentops
    from agentops.sdk.decorators import agent, operation
    SDK_AVAILABLE = True
except ImportError:
    SDK_AVAILABLE = False
    agentops = None

logger = logging.getLogger(__name__)


class HybridAgentOpsService:
    """
    Hybrid service that can use both AgentOps SDK and REST API
    Provides fallback mechanisms and enhanced reliability
    """
    
    def __init__(self, api_key: str, use_sdk: bool = True, use_rest: bool = True):
        self.api_key = api_key
        self.use_sdk = use_sdk and SDK_AVAILABLE
        self.use_rest = use_rest
        self.current_session_id = None
        self.sdk_initialized = False
        self.rest_client = None
        
        # Initialize SDK if available and requested
        if self.use_sdk:
            try:
                agentops.init(
                    api_key=api_key,
                    tags=["rydo-backend", "hybrid-integration", "production"],
                    auto_start_session=False,
                    trace_name="rydo-hybrid-service"
                )
                self.sdk_initialized = True
                logger.info("AgentOps SDK initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize AgentOps SDK: {e}")
                self.use_sdk = False
                
        # Initialize REST client if requested
        if self.use_rest:
            try:
                self.rest_client = AgentOpsRestClient(api_key)
                logger.info("AgentOps REST client initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize AgentOps REST client: {e}")
                self.use_rest = False
                
        if not self.use_sdk and not self.use_rest:
            raise RuntimeError("Neither SDK nor REST API could be initialized")
            
    def start_session(self, session_id: str = None, tags: List[str] = None, 
                     user_id: str = None, agent_id: str = None) -> str:
        """Start a new tracking session using available methods"""
        if not session_id:
            session_id = str(uuid.uuid4())
            
        self.current_session_id = session_id
        session_tags = tags or []
        
        # Add contextual tags
        if user_id:
            session_tags.append(f"user-{user_id}")
        if agent_id:
            session_tags.append(f"agent-{agent_id}")
            
        success_count = 0
        
        # Try SDK first
        if self.use_sdk:
            try:
                agentops.start_session(tags=session_tags)
                success_count += 1
                logger.info(f"SDK session started: {session_id}")
            except Exception as e:
                logger.error(f"Failed to start SDK session: {e}")
                
        # Try REST API
        if self.use_rest:
            try:
                self.rest_client.create_session(
                    session_id=session_id,
                    tags=session_tags
                )
                success_count += 1
                logger.info(f"REST session started: {session_id}")
            except Exception as e:
                logger.error(f"Failed to start REST session: {e}")
                
        if success_count == 0:
            raise RuntimeError("Failed to start session with any method")
            
        return session_id
        
    def end_session(self, state: str = "Success", reason: str = None) -> bool:
        """End the current tracking session"""
        if not self.current_session_id:
            logger.warning("No active session to end")
            return False
            
        success_count = 0
        
        # Try SDK
        if self.use_sdk:
            try:
                agentops.end_session(state)
                success_count += 1
                logger.info(f"SDK session ended: {self.current_session_id}")
            except Exception as e:
                logger.error(f"Failed to end SDK session: {e}")
                
        # Try REST API
        if self.use_rest:
            try:
                self.rest_client.update_session(
                    end_state=state,
                    end_state_reason=reason
                )
                success_count += 1
                logger.info(f"REST session ended: {self.current_session_id}")
            except Exception as e:
                logger.error(f"Failed to end REST session: {e}")
                
        self.current_session_id = None
        return success_count > 0
        
    def track_llm_call(self, model: str, messages: List[Dict], response: Dict,
                      prompt_tokens: int = None, completion_tokens: int = None) -> bool:
        """Track an LLM API call using available methods"""
        success_count = 0
        
        # For SDK, the tracking is automatic if using decorators
        # For REST API, we need to manually create the event
        if self.use_rest:
            try:
                # Extract completion content
                completion_content = ""
                if "choices" in response and len(response["choices"]) > 0:
                    completion_content = response["choices"][0].get("message", {}).get("content", "")
                
                # Get token usage from response if not provided
                if not prompt_tokens or not completion_tokens:
                    usage = response.get("usage", {})
                    prompt_tokens = prompt_tokens or usage.get("prompt_tokens", 0)
                    completion_tokens = completion_tokens or usage.get("completion_tokens", 0)
                
                self.rest_client.create_llm_event(
                    model=model,
                    prompt=messages,
                    completion=completion_content,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens
                )
                success_count += 1
                logger.info(f"LLM call tracked via REST API")
            except Exception as e:
                logger.error(f"Failed to track LLM call via REST API: {e}")
                
        return success_count > 0
        
    def track_tool_usage(self, tool_name: str, input_data: Any, output_data: Any = None) -> bool:
        """Track tool usage"""
        success_count = 0
        
        if self.use_rest:
            try:
                self.rest_client.create_tool_event(
                    name=tool_name,
                    input_data=input_data,
                    output_data=output_data
                )
                success_count += 1
                logger.info(f"Tool usage tracked via REST API: {tool_name}")
            except Exception as e:
                logger.error(f"Failed to track tool usage via REST API: {e}")
                
        return success_count > 0
        
    def create_agent(self, agent_id: str, name: str) -> bool:
        """Register an agent"""
        success_count = 0
        
        if self.use_rest:
            try:
                self.rest_client.create_agent(agent_id, name)
                success_count += 1
                logger.info(f"Agent created via REST API: {agent_id}")
            except Exception as e:
                logger.error(f"Failed to create agent via REST API: {e}")
                
        return success_count > 0
        
    def get_health_status(self) -> Dict:
        """Get health status of all tracking methods"""
        status = {
            "sdk_available": SDK_AVAILABLE,
            "sdk_enabled": self.use_sdk,
            "sdk_initialized": self.sdk_initialized,
            "rest_enabled": self.use_rest,
            "rest_healthy": False,
            "active_session": self.current_session_id,
            "overall_healthy": False
        }
        
        # Check REST API health
        if self.use_rest and self.rest_client:
            try:
                status["rest_healthy"] = self.rest_client.is_healthy()
            except Exception as e:
                logger.error(f"REST health check failed: {e}")
                
        # Overall health
        status["overall_healthy"] = (
            (self.use_sdk and self.sdk_initialized) or 
            (self.use_rest and status["rest_healthy"])
        )
        
        return status
        
    def get_metrics(self) -> Dict:
        """Get service metrics"""
        return {
            "service_type": "hybrid",
            "methods_available": {
                "sdk": self.use_sdk,
                "rest": self.use_rest
            },
            "current_session": self.current_session_id,
            "health": self.get_health_status()
        }


# Decorator for tracking functions with hybrid service
def track_operation(operation_name: str, hybrid_service: HybridAgentOpsService):
    """Decorator to track operations with hybrid service"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Track tool usage
            input_data = {"args": str(args), "kwargs": str(kwargs)}
            
            try:
                result = func(*args, **kwargs)
                hybrid_service.track_tool_usage(
                    tool_name=operation_name,
                    input_data=input_data,
                    output_data=str(result)
                )
                return result
            except Exception as e:
                hybrid_service.track_tool_usage(
                    tool_name=operation_name,
                    input_data=input_data,
                    output_data=f"Error: {str(e)}"
                )
                raise
                
        return wrapper
    return decorator
