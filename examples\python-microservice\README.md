# Enhanced AgentOps Python Microservice

A hybrid Python microservice that integrates with AgentOps using both SDK and REST API approaches for maximum reliability and flexibility.

## Features

- **Hybrid Integration**: Combines AgentOps SDK and REST API
- **Automatic Fallback**: Seamless switching between integration methods
- **OpenAI Integration**: Full support for chat completions and streaming
- **Enhanced Monitoring**: Comprehensive health checks and metrics
- **REST API Endpoints**: Direct AgentOps API access
- **Robust Error Handling**: Detailed logging and error recovery

## Architecture

```
Laravel Backend → Hybrid Python Service → OpenAI API
                        ↓
                [SDK + REST API] → AgentOps Dashboard
```

## Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Run the Service**
   ```bash
   python app.py
   ```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `AGENTOPS_API_KEY` | Your AgentOps API key | Required |
| `AGENTOPS_USE_SDK` | Enable SDK integration | `true` |
| `AGENTOPS_USE_REST` | Enable REST API integration | `true` |
| `OPENAI_API_KEY` | Your OpenAI API key | Required |
| `SERVICE_PORT` | Service port | `5000` |
| `SERVICE_HOST` | Service host | `0.0.0.0` |
| `LOG_LEVEL` | Logging level | `INFO` |

### Integration Modes

- **SDK Only**: Set `AGENTOPS_USE_REST=false`
- **REST Only**: Set `AGENTOPS_USE_SDK=false`
- **Hybrid** (Recommended): Both enabled for maximum reliability

## API Endpoints

### Chat Endpoints

#### POST /chat/completions
Standard OpenAI-compatible chat completions with AgentOps tracking.

```json
{
  "model": "gpt-4",
  "messages": [{"role": "user", "content": "Hello"}],
  "session_id": "optional-session-id",
  "user_id": "user-123",
  "agent_id": "agent-456"
}
```

#### POST /chat/stream
Streaming chat completions with AgentOps tracking.

### AgentOps Direct Endpoints

#### POST /agentops/session
Create a new AgentOps session.

#### PUT /agentops/session/{session_id}
Update or end an AgentOps session.

#### POST /agentops/events
Create events in AgentOps.

#### POST /agentops/agent
Register a new agent.

### Monitoring Endpoints

#### GET /health
Service health check with AgentOps status.

#### GET /metrics
Detailed service metrics and configuration.

## Health Monitoring

The service provides comprehensive health monitoring:

```bash
curl http://localhost:5000/health
```

Response includes:
- Service status
- SDK availability and initialization
- REST API connectivity
- Current session information

## Error Handling

The hybrid service provides robust error handling:

1. **SDK Failure**: Automatically falls back to REST API
2. **REST API Failure**: Uses SDK if available
3. **Complete Failure**: Continues OpenAI operations without tracking
4. **Detailed Logging**: All errors are logged with context

## Integration with Laravel

The service is designed to work seamlessly with your Laravel backend:

1. **Zero Code Changes**: Existing OpenAI calls work unchanged
2. **Enhanced Tracking**: Automatic session and event tracking
3. **Fallback Support**: Continues working even if AgentOps fails
4. **Rich Metadata**: Tracks user, agent, and session information

## Development

### Running in Development

```bash
# Install dependencies
pip install -r requirements.txt

# Set up environment
cp .env.example .env

# Run with debug logging
LOG_LEVEL=DEBUG python app.py
```

### Testing

```bash
# Test health endpoint
curl http://localhost:5000/health

# Test chat completion
curl -X POST http://localhost:5000/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [{"role": "user", "content": "Hello"}],
    "session_id": "test-session",
    "user_id": "test-user"
  }'
```

## Deployment

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Production Considerations

1. **Load Balancing**: Run multiple instances behind a load balancer
2. **Health Checks**: Monitor the `/health` endpoint
3. **Logging**: Configure appropriate log levels
4. **Security**: Use HTTPS and secure API key storage
5. **Monitoring**: Set up alerts for service failures

## Troubleshooting

### Common Issues

1. **AgentOps SDK Import Error**
   - Install agentops: `pip install agentops>=0.3.0`
   - Check Python version compatibility

2. **REST API Connection Failed**
   - Verify internet connectivity
   - Check AgentOps API key validity
   - Ensure firewall allows outbound HTTPS

3. **OpenAI API Errors**
   - Verify OpenAI API key
   - Check rate limits and quotas

### Debug Commands

```bash
# Check service health
curl http://localhost:5000/health

# View detailed metrics
curl http://localhost:5000/metrics

# Test AgentOps session creation
curl -X POST http://localhost:5000/agentops/session \
  -H "Content-Type: application/json" \
  -d '{"tags": ["test"]}'
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is part of the Rydo backend and follows the same licensing terms.

## Support

For issues related to:
- **AgentOps Integration**: Check the AgentOps documentation
- **OpenAI API**: Refer to OpenAI documentation
- **Service Issues**: Check logs and health endpoints
- **Laravel Integration**: Refer to the main integration guide
