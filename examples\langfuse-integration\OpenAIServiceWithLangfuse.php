<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class OpenAIServiceWithLangfuse
{
    public function __construct(
        private LangfuseService $langfuse
    ) {}
    
    /**
     * Send chat completion with <PERSON><PERSON> tracking
     */
    public function chatCompletion(array $messages, array $options = []): array
    {
        $sessionId = $options['session_id'] ?? Str::uuid();
        $userId = $options['user_id'] ?? null;
        $generationId = Str::uuid();
        
        // Create trace in Langfuse
        $this->langfuse->createTrace($sessionId, $userId);
        
        $startTime = now();
        
        try {
            // Create generation record
            $this->langfuse->createGeneration([
                'id' => $generationId,
                'trace_id' => $sessionId,
                'name' => 'openai-chat-completion',
                'start_time' => $startTime->toISOString(),
                'model' => $options['model'] ?? 'gpt-4',
                'temperature' => $options['temperature'] ?? 0.7,
                'max_tokens' => $options['max_tokens'] ?? 1000,
                'messages' => $messages,
                'metadata' => [
                    'user_id' => $userId,
                    'session_id' => $sessionId,
                    'application' => 'rydo-backend',
                ],
            ]);
            
            // Make OpenAI API call
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('services.openai.api_key'),
                'Content-Type' => 'application/json',
            ])->post('https://api.openai.com/v1/chat/completions', [
                'model' => $options['model'] ?? 'gpt-4',
                'messages' => $messages,
                'temperature' => $options['temperature'] ?? 0.7,
                'max_tokens' => $options['max_tokens'] ?? 1000,
            ]);
            
            $endTime = now();
            $responseData = $response->json();
            
            if ($response->successful()) {
                // Update generation with completion data
                $this->langfuse->updateGeneration($generationId, [
                    'end_time' => $endTime->toISOString(),
                    'response' => $responseData['choices'][0]['message']['content'] ?? '',
                    'prompt_tokens' => $responseData['usage']['prompt_tokens'] ?? 0,
                    'completion_tokens' => $responseData['usage']['completion_tokens'] ?? 0,
                    'total_tokens' => $responseData['usage']['total_tokens'] ?? 0,
                    'metadata' => [
                        'finish_reason' => $responseData['choices'][0]['finish_reason'] ?? null,
                        'response_id' => $responseData['id'] ?? null,
                        'duration_ms' => $startTime->diffInMilliseconds($endTime),
                    ],
                ]);
                
                return $responseData;
            }
            
            throw new \Exception('OpenAI API error: ' . $response->body());
            
        } catch (\Exception $e) {
            // Log error to Langfuse
            $this->langfuse->updateGeneration($generationId, [
                'end_time' => now()->toISOString(),
                'metadata' => [
                    'error' => $e->getMessage(),
                    'duration_ms' => $startTime->diffInMilliseconds(now()),
                ],
            ]);
            
            Log::error('OpenAI API call failed', [
                'error' => $e->getMessage(),
                'session_id' => $sessionId,
                'generation_id' => $generationId,
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Calculate cost based on token usage
     */
    public function calculateCost(string $model, int $promptTokens, int $completionTokens): float
    {
        // OpenAI pricing (as of 2025)
        $pricing = [
            'gpt-4' => [
                'prompt' => 0.03 / 1000,    // $0.03 per 1K prompt tokens
                'completion' => 0.06 / 1000, // $0.06 per 1K completion tokens
            ],
            'gpt-3.5-turbo' => [
                'prompt' => 0.0015 / 1000,   // $0.0015 per 1K prompt tokens
                'completion' => 0.002 / 1000, // $0.002 per 1K completion tokens
            ],
        ];
        
        if (!isset($pricing[$model])) {
            return 0.0;
        }
        
        $promptCost = $promptTokens * $pricing[$model]['prompt'];
        $completionCost = $completionTokens * $pricing[$model]['completion'];
        
        return round($promptCost + $completionCost, 6);
    }
}
